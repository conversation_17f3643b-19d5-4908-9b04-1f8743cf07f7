import asyncio
from asyncio import AbstractEventLoop
import argparse
from loguru import logger
from beezer.app import app
from beezer.plugins.flows._basic import BasicFlow
from beezer.plugins import load_plugins
from beezer.license.main import init_system, do_verify

# Load plugins at startup
load_plugins(app.config)


async def run_flows():
    specified_flows = app.config.get("specified_flows", {})

    for flow_config in app.config.flows:
        # Skip flows that are not specified when specified_flows is not empty
        if specified_flows and flow_config.name not in specified_flows:
            logger.debug(f"Skipping flow {flow_config.name} as it was not specified")
            continue

        # If specific inbounds are specified for this flow, filter the flow's inbounds
        if specified_flows and flow_config.name in specified_flows:
            specified_inbounds = specified_flows[flow_config.name]
            if specified_inbounds:  # If inbounds are specified
                flow_config.inbounds = [
                    inb for inb in flow_config.inbounds if inb in specified_inbounds
                ]
                logger.debug(
                    f"Filtered inbounds for flow {flow_config.name}: {flow_config.inbounds}"
                )

        # Create flow instance with app config
        flow = BasicFlow(flow_config=flow_config, app_config=app.config)
        # Initialize flow through execute
        await flow.execute()


def main():
    # Parse command line arguments
    parser = argparse.ArgumentParser(description="Run specified flows")
    parser.add_argument(
        "--flow",
        type=str,
        action="append",
        help="Specify a flow and its inbounds in format: flow_name:inbound1,inbound2",
        default=[],
    )
    args = parser.parse_args()

    # Parse flows and their inbounds
    specified_flows = {}
    for flow_spec in args.flow:
        if ":" in flow_spec:
            flow_name, inbounds = flow_spec.split(":")
            specified_flows[flow_name.strip()] = [
                inb.strip() for inb in inbounds.split(",")
            ]
        else:
            specified_flows[flow_spec.strip()] = []  # Empty list means all inbounds

    # Add specified flows to app config
    app.config.specified_flows = specified_flows

    # Run the event loop
    loop = asyncio.get_event_loop()
    try:
        # Initialize system and verify license
        # init_system()
        do_verify(loop)

        # Run flows after license verification
        loop.run_until_complete(run_flows())
        loop.run_forever()
    except KeyboardInterrupt:
        logger.info("Received exit signal, shutting down...")
    finally:
        loop.close()


if __name__ == "__main__":
    main()
