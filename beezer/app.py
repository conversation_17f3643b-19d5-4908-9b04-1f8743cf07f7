import time
import asyncio
from asyncio import AbstractEventLoop
from sanic import Sanic
from sanic.blueprints import Blueprint
from sanic.worker.loader import AppLoader
from sanic.request import Request
from sanic.response import json
from pydantic import BaseModel
from typing import List, Optional
from enum import Enum
from functools import partial
from beezer.config import YamlConfig
from loguru import logger


app = Sanic("beezer", config=YamlConfig())
app.config.TOUCHUP = False
