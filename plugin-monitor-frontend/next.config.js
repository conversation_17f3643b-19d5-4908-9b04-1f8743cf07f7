/** @type {import('next').NextConfig} */
const nextConfig = {
  experimental: {
    appDir: true,
  },
  transpilePackages: ['antd', '@ant-design/icons', '@ant-design/nextjs-registry'],
  async rewrites() {
    return [
      {
        source: '/api/beezer/:path*',
        destination: 'http://localhost:9999/api/:path*',
      },
    ];
  },
  webpack: (config) => {
    config.resolve.alias = {
      ...config.resolve.alias,
      '@': require('path').resolve(__dirname, './'),
    };
    return config;
  },
  images: {
    domains: ['localhost'],
  },
  env: {
    BEEZER_API_URL: process.env.BEEZER_API_URL || 'http://localhost:9999',
    WEBSOCKET_URL: process.env.WEBSOCKET_URL || 'ws://localhost:9999',
  },
};

module.exports = nextConfig;
