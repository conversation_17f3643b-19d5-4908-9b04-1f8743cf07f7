'use client';

import React, { useState } from 'react';
import { Layout, Card, Button, Space, message, Drawer, Tabs } from 'antd';
import {
  AppstoreAddOutlined,
  SaveOutlined,
  EyeOutlined,
  SettingOutlined,
  DragOutlined,
} from '@ant-design/icons';

const { Sider, Content } = Layout;
const { TabPane } = Tabs;

// 组件库数据
const widgetLibrary = [
  {
    id: 'number-display',
    name: '数字显示器',
    icon: '🔢',
    category: 'display',
    description: '显示数值数据',
  },
  {
    id: 'gauge-chart',
    name: '仪表盘',
    icon: '⏲️',
    category: 'chart',
    description: '圆形仪表盘图表',
  },
  {
    id: 'line-chart',
    name: '折线图',
    icon: '📈',
    category: 'chart',
    description: '时间序列折线图',
  },
  {
    id: 'status-indicator',
    name: '状态指示器',
    icon: '🚦',
    category: 'display',
    description: '显示状态信息',
  },
  {
    id: 'button',
    name: '按钮',
    icon: '🔘',
    category: 'control',
    description: '交互按钮',
  },
  {
    id: 'text-label',
    name: '文本标签',
    icon: '📝',
    category: 'display',
    description: '静态文本显示',
  },
];

const categories = [
  { key: 'all', label: '全部组件' },
  { key: 'display', label: '显示组件' },
  { key: 'chart', label: '图表组件' },
  { key: 'control', label: '控制组件' },
];

export default function HMIPage() {
  const [isPreviewMode, setIsPreviewMode] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [canvasWidgets, setCanvasWidgets] = useState<any[]>([]);
  const [propertyDrawerOpen, setPropertyDrawerOpen] = useState(false);
  const [selectedWidget, setSelectedWidget] = useState<any>(null);

  const handleDragStart = (e: React.DragEvent, widget: any) => {
    e.dataTransfer.setData('application/json', JSON.stringify(widget));
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    const widgetData = JSON.parse(e.dataTransfer.getData('application/json'));
    const rect = (e.currentTarget as HTMLElement).getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;

    const newWidget = {
      ...widgetData,
      id: `${widgetData.id}-${Date.now()}`,
      x,
      y,
      width: 200,
      height: 100,
    };

    setCanvasWidgets([...canvasWidgets, newWidget]);
    message.success(`已添加 ${widgetData.name}`);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
  };

  const handleWidgetClick = (widget: any) => {
    setSelectedWidget(widget);
    setPropertyDrawerOpen(true);
  };

  const handleSave = () => {
    // TODO: 保存配置到后端
    message.success('配置已保存');
  };

  const togglePreview = () => {
    setIsPreviewMode(!isPreviewMode);
    message.info(isPreviewMode ? '已切换到编辑模式' : '已切换到预览模式');
  };

  const filteredWidgets = selectedCategory === 'all' 
    ? widgetLibrary 
    : widgetLibrary.filter(widget => widget.category === selectedCategory);

  return (
    <div className="h-full">
      <Layout className="h-full">
        {/* 左侧组件库 */}
        {!isPreviewMode && (
          <Sider width={280} className="bg-white border-r">
            <div className="p-4">
              <h3 className="text-lg font-semibold mb-4">组件库</h3>
              
              {/* 分类标签 */}
              <Tabs
                activeKey={selectedCategory}
                onChange={setSelectedCategory}
                size="small"
                className="mb-4"
              >
                {categories.map(category => (
                  <TabPane tab={category.label} key={category.key} />
                ))}
              </Tabs>

              {/* 组件列表 */}
              <div className="space-y-2">
                {filteredWidgets.map((widget) => (
                  <Card
                    key={widget.id}
                    size="small"
                    className="cursor-move hover:shadow-md transition-shadow"
                    draggable
                    onDragStart={(e) => handleDragStart(e, widget)}
                  >
                    <div className="flex items-center space-x-3">
                      <span className="text-2xl">{widget.icon}</span>
                      <div className="flex-1 min-w-0">
                        <div className="font-medium text-sm">{widget.name}</div>
                        <div className="text-xs text-gray-500 truncate">
                          {widget.description}
                        </div>
                      </div>
                      <DragOutlined className="text-gray-400" />
                    </div>
                  </Card>
                ))}
              </div>
            </div>
          </Sider>
        )}

        {/* 主内容区 */}
        <Layout>
          {/* 工具栏 */}
          <div className="bg-white border-b px-4 py-2">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-lg font-semibold">HMI 组态设计器</h2>
              </div>
              <Space>
                <Button
                  icon={<EyeOutlined />}
                  onClick={togglePreview}
                  type={isPreviewMode ? 'primary' : 'default'}
                >
                  {isPreviewMode ? '编辑模式' : '预览模式'}
                </Button>
                <Button icon={<SaveOutlined />} onClick={handleSave}>
                  保存
                </Button>
                <Button icon={<SettingOutlined />}>
                  设置
                </Button>
              </Space>
            </div>
          </div>

          {/* 画布区域 */}
          <Content className="bg-gray-100 p-4">
            <div
              className="bg-white rounded-lg shadow-sm h-full relative overflow-hidden"
              onDrop={handleDrop}
              onDragOver={handleDragOver}
              style={{
                backgroundImage: isPreviewMode ? 'none' : 
                  'radial-gradient(circle, #e5e7eb 1px, transparent 1px)',
                backgroundSize: '20px 20px',
              }}
            >
              {canvasWidgets.length === 0 && !isPreviewMode && (
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="text-center text-gray-400">
                    <AppstoreAddOutlined className="text-4xl mb-2" />
                    <div className="text-lg">拖拽组件到此处开始设计</div>
                    <div className="text-sm">从左侧组件库选择组件拖拽到画布</div>
                  </div>
                </div>
              )}

              {/* 渲染画布上的组件 */}
              {canvasWidgets.map((widget) => (
                <div
                  key={widget.id}
                  className={`absolute border-2 ${
                    isPreviewMode ? 'border-transparent' : 'border-dashed border-blue-300'
                  } cursor-pointer hover:border-blue-500 transition-colors`}
                  style={{
                    left: widget.x,
                    top: widget.y,
                    width: widget.width,
                    height: widget.height,
                  }}
                  onClick={() => !isPreviewMode && handleWidgetClick(widget)}
                >
                  <div className="w-full h-full bg-white rounded shadow-sm flex items-center justify-center">
                    <div className="text-center">
                      <div className="text-2xl mb-1">{widget.icon}</div>
                      <div className="text-sm font-medium">{widget.name}</div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </Content>
        </Layout>
      </Layout>

      {/* 属性配置抽屉 */}
      <Drawer
        title="组件属性"
        placement="right"
        width={320}
        open={propertyDrawerOpen}
        onClose={() => setPropertyDrawerOpen(false)}
      >
        {selectedWidget && (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-1">组件名称</label>
              <div className="text-sm text-gray-600">{selectedWidget.name}</div>
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">位置</label>
              <div className="text-sm text-gray-600">
                X: {selectedWidget.x}px, Y: {selectedWidget.y}px
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">尺寸</label>
              <div className="text-sm text-gray-600">
                宽: {selectedWidget.width}px, 高: {selectedWidget.height}px
              </div>
            </div>
            {/* TODO: 添加更多属性配置 */}
          </div>
        )}
      </Drawer>
    </div>
  );
}
