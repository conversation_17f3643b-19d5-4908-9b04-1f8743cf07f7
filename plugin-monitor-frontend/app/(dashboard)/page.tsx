import React from 'react';
import { Card, Row, Col, Statistic, Progress, Timeline, Tabs } from 'antd';
import {
  PluginOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  ClockCircleOutlined,
  DashboardOutlined,
  BarChartOutlined,
} from '@ant-design/icons';
import { RealTimeDashboard } from '@/components/charts/real-time-dashboard';

const { TabPane } = Tabs;

export default function DashboardPage() {
  // 模拟数据
  const stats = {
    totalPlugins: 12,
    runningPlugins: 8,
    errorPlugins: 2,
    idlePlugins: 2,
  };

  const recentActivities = [
    {
      time: '2 分钟前',
      content: '插件 "Modbus-001" 状态变更为运行中',
      type: 'success',
    },
    {
      time: '5 分钟前',
      content: '插件 "HTTP-Client-002" 连接超时',
      type: 'error',
    },
    {
      time: '10 分钟前',
      content: '新增插件 "MQTT-Publisher-003"',
      type: 'info',
    },
    {
      time: '15 分钟前',
      content: '系统配置已更新',
      type: 'info',
    },
  ];

  // 系统总览组件
  const SystemOverview = () => (
    <div className="space-y-6">

      {/* 统计卡片 */}
      <Row gutter={[16, 16]} className="mb-6">
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="总插件数"
              value={stats.totalPlugins}
              prefix={<PluginOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="运行中"
              value={stats.runningPlugins}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="错误状态"
              value={stats.errorPlugins}
              prefix={<ExclamationCircleOutlined />}
              valueStyle={{ color: '#ff4d4f' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="空闲状态"
              value={stats.idlePlugins}
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
      </Row>

      <Row gutter={[16, 16]}>
        {/* 系统健康度 */}
        <Col xs={24} lg={12}>
          <Card title="系统健康度" className="h-full">
            <div className="space-y-4">
              <div>
                <div className="flex justify-between mb-2">
                  <span>插件运行率</span>
                  <span className="text-success-500 font-medium">
                    {Math.round((stats.runningPlugins / stats.totalPlugins) * 100)}%
                  </span>
                </div>
                <Progress
                  percent={Math.round((stats.runningPlugins / stats.totalPlugins) * 100)}
                  status="active"
                  strokeColor="#52c41a"
                />
              </div>
              
              <div>
                <div className="flex justify-between mb-2">
                  <span>系统稳定性</span>
                  <span className="text-success-500 font-medium">95%</span>
                </div>
                <Progress
                  percent={95}
                  strokeColor="#52c41a"
                />
              </div>
              
              <div>
                <div className="flex justify-between mb-2">
                  <span>内存使用率</span>
                  <span className="text-warning-500 font-medium">68%</span>
                </div>
                <Progress
                  percent={68}
                  strokeColor="#faad14"
                />
              </div>
            </div>
          </Card>
        </Col>

        {/* 最近活动 */}
        <Col xs={24} lg={12}>
          <Card title="最近活动" className="h-full">
            <Timeline
              items={recentActivities.map((activity, index) => ({
                color: activity.type === 'success' ? 'green' : 
                       activity.type === 'error' ? 'red' : 'blue',
                children: (
                  <div key={index}>
                    <p className="text-sm text-gray-800 mb-1">{activity.content}</p>
                    <p className="text-xs text-gray-500">{activity.time}</p>
                  </div>
                ),
              }))}
            />
          </Card>
        </Col>
      </Row>
    </div>
  );
}
