'use client';

import React, { useEffect, useState } from 'react';
import { Row, Col, Input, Select, Button, Space, message, Spin, Tabs } from 'antd';
import { SearchOutlined, ReloadOutlined, FilterOutlined, AppstoreOutlined, UnorderedListOutlined } from '@ant-design/icons';
import { usePluginStore } from '@/lib/stores/plugin-store';
import { PluginCard } from '@/components/plugin/plugin-card';
import { PluginList } from '@/components/plugin/plugin-list';
import { PluginDetail } from '@/components/plugin/plugin-detail';
import { StatusStats } from '@/components/ui/status-indicator';
import { PluginStatus, PluginType } from '@/lib/types/plugin';
import { useWebSocket } from '@/lib/services/websocket';

const { Search } = Input;
const { Option } = Select;
const { TabPane } = Tabs;

export default function PluginsPage() {
  const {
    plugins,
    pluginConfigs,
    stats,
    loading,
    error,
    fetchPluginStatus,
    fetchPluginConfigs,
    executePluginAction,
    updatePluginStatus,
    clearError,
  } = usePluginStore();

  const [searchText, setSearchText] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [typeFilter, setTypeFilter] = useState<string>('all');
  const [viewMode, setViewMode] = useState<'card' | 'list'>('card');
  const [selectedPluginId, setSelectedPluginId] = useState<string | null>(null);
  const [detailDrawerOpen, setDetailDrawerOpen] = useState(false);

  // WebSocket 连接
  const { connected, subscribe, subscribeToPluginStatus } = useWebSocket();

  useEffect(() => {
    // 初始化数据
    fetchPluginStatus();
    fetchPluginConfigs();

    // 设置定时刷新
    const interval = setInterval(() => {
      fetchPluginStatus();
    }, 5000); // 每5秒刷新一次

    return () => clearInterval(interval);
  }, [fetchPluginStatus, fetchPluginConfigs]);

  // WebSocket 实时数据订阅
  useEffect(() => {
    if (connected) {
      // 订阅插件状态更新
      const unsubscribeStatus = subscribe('plugin_status', (data) => {
        updatePluginStatus(data.plugin_id, data);
      });

      // 订阅连接状态
      const unsubscribeConnection = subscribe('connection', (data) => {
        if (data.status === 'connected') {
          message.success('实时连接已建立');
          subscribeToPluginStatus();
        } else if (data.status === 'disconnected') {
          message.warning('实时连接已断开');
        }
      });

      return () => {
        unsubscribeStatus();
        unsubscribeConnection();
      };
    }
  }, [connected, subscribe, subscribeToPluginStatus, updatePluginStatus]);

  useEffect(() => {
    if (error) {
      message.error(error);
      clearError();
    }
  }, [error, clearError]);

  const handlePluginAction = async (action: string, pluginId: string) => {
    try {
      switch (action) {
        case 'start':
        case 'stop':
        case 'restart':
          await executePluginAction(pluginId, action);
          message.success(`插件${action}操作成功`);
          // 刷新状态
          setTimeout(() => fetchPluginStatus(), 1000);
          break;
        case 'details':
          setSelectedPluginId(pluginId);
          setDetailDrawerOpen(true);
          break;
        case 'configure':
          // TODO: 打开配置弹窗
          message.info('配置功能开发中...');
          break;
        case 'delete':
          // TODO: 删除确认
          message.info('删除功能开发中...');
          break;
        default:
          message.warning('未知操作');
      }
    } catch (err) {
      message.error(`操作失败: ${err instanceof Error ? err.message : '未知错误'}`);
    }
  };

  const handleRefresh = () => {
    fetchPluginStatus();
    fetchPluginConfigs();
  };

  // 过滤插件
  const filteredPlugins = Object.values(plugins).filter((plugin) => {
    // 搜索过滤
    if (searchText) {
      const config = pluginConfigs[plugin.plugin_id];
      const searchLower = searchText.toLowerCase();
      const matchesSearch = 
        plugin.plugin_id.toLowerCase().includes(searchLower) ||
        (config?.name?.toLowerCase().includes(searchLower)) ||
        plugin.plugin_type.toLowerCase().includes(searchLower);
      
      if (!matchesSearch) return false;
    }

    // 状态过滤
    if (statusFilter !== 'all' && plugin.status !== statusFilter) {
      return false;
    }

    // 类型过滤
    if (typeFilter !== 'all' && plugin.plugin_type !== typeFilter) {
      return false;
    }

    return true;
  });

  return (
    <div className="p-6">
      {/* 页面头部 */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-800 mb-2">插件监控</h1>
            <p className="text-gray-600">实时监控所有插件的运行状态和性能指标</p>
          </div>
          <Button
            type="primary"
            icon={<ReloadOutlined />}
            onClick={handleRefresh}
            loading={loading}
          >
            刷新
          </Button>
        </div>

        {/* 统计信息 */}
        <div className="bg-white p-4 rounded-lg shadow-sm border mb-4">
          <div className="flex items-center justify-between">
            <div>
              <span className="text-sm text-gray-500">插件总数: </span>
              <span className="text-lg font-semibold">{stats.total}</span>
            </div>
            <StatusStats stats={stats} />
          </div>
        </div>

        {/* 搜索和过滤 */}
        <div className="bg-white p-4 rounded-lg shadow-sm border">
          <div className="flex items-center justify-between">
            <Space size="middle" wrap>
              <Search
                placeholder="搜索插件名称或ID"
                allowClear
                style={{ width: 300 }}
                value={searchText}
                onChange={(e) => setSearchText(e.target.value)}
                prefix={<SearchOutlined />}
              />

              <Select
                placeholder="状态筛选"
                style={{ width: 120 }}
                value={statusFilter}
                onChange={setStatusFilter}
              >
                <Option value="all">全部状态</Option>
                <Option value={PluginStatus.RUNNING}>运行中</Option>
                <Option value={PluginStatus.ERROR}>错误</Option>
                <Option value={PluginStatus.IDLE}>空闲</Option>
                <Option value={PluginStatus.STOPPED}>已停止</Option>
              </Select>

              <Select
                placeholder="类型筛选"
                style={{ width: 120 }}
                value={typeFilter}
                onChange={setTypeFilter}
              >
                <Option value="all">全部类型</Option>
                <Option value={PluginType.INBOUND}>输入插件</Option>
                <Option value={PluginType.OUTBOUND}>输出插件</Option>
                <Option value={PluginType.RULE}>规则插件</Option>
                <Option value={PluginType.FLOW}>流程插件</Option>
              </Select>

              <Button icon={<FilterOutlined />}>
                高级筛选
              </Button>
            </Space>

            {/* 视图切换 */}
            <Space>
              <Button.Group>
                <Button
                  icon={<AppstoreOutlined />}
                  type={viewMode === 'card' ? 'primary' : 'default'}
                  onClick={() => setViewMode('card')}
                >
                  卡片视图
                </Button>
                <Button
                  icon={<UnorderedListOutlined />}
                  type={viewMode === 'list' ? 'primary' : 'default'}
                  onClick={() => setViewMode('list')}
                >
                  列表视图
                </Button>
              </Button.Group>
            </Space>
          </div>
        </div>
      </div>

      {/* 插件列表 */}
      <Spin spinning={loading}>
        {filteredPlugins.length > 0 ? (
          viewMode === 'card' ? (
            <Row gutter={[16, 16]}>
              {filteredPlugins.map((plugin) => (
                <Col xs={24} sm={12} lg={8} xl={6} key={plugin.plugin_id}>
                  <PluginCard
                    plugin={plugin}
                    config={pluginConfigs[plugin.plugin_id]}
                    onAction={handlePluginAction}
                  />
                </Col>
              ))}
            </Row>
          ) : (
            <PluginList
              plugins={filteredPlugins}
              configs={pluginConfigs}
              loading={loading}
              onAction={handlePluginAction}
              onRefresh={handleRefresh}
            />
          )
        ) : (
          <div className="text-center py-12">
            <div className="text-gray-400 text-lg mb-2">
              {searchText || statusFilter !== 'all' || typeFilter !== 'all'
                ? '没有找到匹配的插件'
                : '暂无插件数据'}
            </div>
            <div className="text-gray-500 text-sm">
              {searchText || statusFilter !== 'all' || typeFilter !== 'all'
                ? '请尝试调整搜索条件或筛选器'
                : '请检查系统连接状态'}
            </div>
          </div>
        )}
      </Spin>

      {/* 插件详情抽屉 */}
      <PluginDetail
        open={detailDrawerOpen}
        pluginId={selectedPluginId}
        plugin={selectedPluginId ? plugins[selectedPluginId] : undefined}
        config={selectedPluginId ? pluginConfigs[selectedPluginId] : undefined}
        onClose={() => {
          setDetailDrawerOpen(false);
          setSelectedPluginId(null);
        }}
        onAction={handlePluginAction}
      />
    </div>
  );
}
