'use client';

import React, { useState } from 'react';
import { Layout, Menu, Button, Avatar, Dropdown, Badge } from 'antd';
import {
  DashboardOutlined,
  PluginOutlined,
  SettingOutlined,
  BellOutlined,
  UserOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  MonitorOutlined,
  AppstoreOutlined,
} from '@ant-design/icons';
import Link from 'next/link';
import { usePathname } from 'next/navigation';

const { Header, Sider, Content } = Layout;

const menuItems = [
  {
    key: '/dashboard',
    icon: <DashboardOutlined />,
    label: <Link href="/dashboard">总览</Link>,
  },
  {
    key: '/dashboard/plugins',
    icon: <PluginOutlined />,
    label: <Link href="/dashboard/plugins">插件监控</Link>,
  },
  {
    key: '/dashboard/hmi',
    icon: <MonitorOutlined />,
    label: <Link href="/dashboard/hmi">HMI组态</Link>,
  },
  {
    key: '/dashboard/settings',
    icon: <SettingOutlined />,
    label: <Link href="/dashboard/settings">系统设置</Link>,
  },
];

const userMenuItems = [
  {
    key: 'profile',
    label: '个人资料',
    icon: <UserOutlined />,
  },
  {
    key: 'settings',
    label: '偏好设置',
    icon: <SettingOutlined />,
  },
  {
    type: 'divider' as const,
  },
  {
    key: 'logout',
    label: '退出登录',
    danger: true,
  },
];

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const [collapsed, setCollapsed] = useState(false);
  const pathname = usePathname();

  return (
    <Layout className="min-h-screen">
      <Sider
        trigger={null}
        collapsible
        collapsed={collapsed}
        className="shadow-lg"
        width={240}
      >
        <div className="flex items-center justify-center h-16 bg-white border-b">
          <div className="flex items-center space-x-2">
            <AppstoreOutlined className="text-2xl text-primary-500" />
            {!collapsed && (
              <span className="text-lg font-semibold text-gray-800">
                Plugin Monitor
              </span>
            )}
          </div>
        </div>
        <Menu
          theme="light"
          mode="inline"
          selectedKeys={[pathname]}
          items={menuItems}
          className="border-r-0"
        />
      </Sider>
      
      <Layout>
        <Header className="bg-white shadow-sm px-4 flex items-center justify-between">
          <div className="flex items-center">
            <Button
              type="text"
              icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
              onClick={() => setCollapsed(!collapsed)}
              className="text-lg"
            />
          </div>
          
          <div className="flex items-center space-x-4">
            <Badge count={3} size="small">
              <Button
                type="text"
                icon={<BellOutlined />}
                className="text-lg"
              />
            </Badge>
            
            <Dropdown
              menu={{ items: userMenuItems }}
              placement="bottomRight"
              arrow
            >
              <div className="flex items-center space-x-2 cursor-pointer hover:bg-gray-50 px-2 py-1 rounded">
                <Avatar size="small" icon={<UserOutlined />} />
                <span className="text-sm text-gray-700">管理员</span>
              </div>
            </Dropdown>
          </div>
        </Header>
        
        <Content className="p-6 bg-gray-50">
          <div className="bg-white rounded-lg shadow-sm min-h-full">
            {children}
          </div>
        </Content>
      </Layout>
    </Layout>
  );
}
