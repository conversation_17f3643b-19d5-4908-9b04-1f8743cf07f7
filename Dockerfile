FROM ubuntu:24.04
RUN sed -i 's/http:\/\/archive.ubuntu.com/http:\/\/mirrors.aliyun.com/g' /etc/apt/sources.list.d/ubuntu.sources && \
    sed -i 's/http:\/\/security.ubuntu.com/http:\/\/mirrors.aliyun.com/g' /etc/apt/sources.list.d/ubuntu.sources
RUN dpkg --add-architecture i386 

RUN apt-get update && apt install -y python3.12 wget dmidecode curl locales qemu-user-static binfmt-support
ENV PYTHONIOENCODING=utf-8
ENV LANG=C.UTF-8
ENV LC_ALL=C.UTF-8

RUN update-binfmts --install  i386 /usr/bin/qemu-i386-static --magic '\x7fELF\x01\x01\x01\x03\x00\x00\x00\x00\x00\x00\x00\x00\x03\x00\x03\x00\x01\x00\x00\x00' --mask '\xff\xff\xff\xff\xff\xff\xff\xfc\xff\xff\xff\xff\xff\xff\xff\xff\xf8\xff\xff\xff\xff\xff\xff\xff'

RUN mkdir -pm755 /etc/apt/keyrings
RUN wget -O /etc/apt/keyrings/winehq-archive.key https://dl.winehq.org/wine-builds/winehq.key
# RUN echo "Types: deb\n\
# URIs: https://dl.winehq.org/wine-builds/ubuntu\n\
# Suites: noble\n\
# Components: main\n\
# Architectures: amd64 i386\n\
# Signed-By: /etc/apt/keyrings/winehq-archive.key" > /etc/apt/sources.list.d/winehq-noble.sources
RUN echo "Types: deb\n\
URIs: https://mirrors.tuna.tsinghua.edu.cn/wine-builds/ubuntu/\n\
Suites: noble\n\
Components: main\n\
Architectures: amd64 i386\n\
Signed-By: /etc/apt/keyrings/winehq-archive.key" > /etc/apt/sources.list.d/winehq-noble.sources

# RUN wget -NP /etc/apt/sources.list.d/ https://dl.winehq.org/wine-builds/ubuntu/dists/noble/winehq-noble.sources
RUN apt-get update

RUN apt install --install-recommends winehq-stable -y
RUN apt install -y libc6:i386 libncurses6:i386 libstdc++6:i386 zlib1g:i386 zlib1g-dev:i386
RUN apt install supervisor -y

# 创建supervisor日志目录
RUN mkdir -p /var/log/supervisor
WORKDIR /app
COPY dist/main /app/main
COPY dist/worker /app/worker
# COPY dist/gui /app/gui
COPY bins /app/

# 拷贝supervisor配置文件到系统目录
COPY supervisord.conf /etc/supervisor/supervisord.conf


# 创建supervisor配置目录和日志目录
RUN mkdir -p /etc/supervisor/conf.d

# 创建 entrypoint 脚本，在容器启动时先启动 binfmt-support 服务
RUN echo '#!/bin/sh\nservice binfmt-support start\nexec "$@"' > /usr/local/bin/docker-entrypoint.sh && \
    chmod +x /usr/local/bin/docker-entrypoint.sh

ENTRYPOINT ["docker-entrypoint.sh", "supervisord", "-c", "/etc/supervisor/supervisord.conf"]
# CMD ["sleep", "inf"]